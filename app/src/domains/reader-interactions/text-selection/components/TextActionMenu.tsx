import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Share2, MessageSquare, Highlighter, Bookmark, Search, FileText, Palette } from 'lucide-react';
// ✅ YENİ ORGANİZE YAPISI - Feature-based imports
import { useReaderInteractionStyles } from '../../shared/hooks/useReaderInteractionStyles';

// RGB formatına dönüştürme fonksiyonu
const hexToRgb = (hex: string): string => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (result) {
    const r = parseInt(result[1], 16);
    const g = parseInt(result[2], 16);
    const b = parseInt(result[3], 16);
    return `rgb(${r}, ${g}, ${b})`;
  }
  return hex;
};

interface TextActionMenuProps {
  isVisible: boolean;
  position: { top: number; left: number; placement?: 'above' | 'below' } | null;
  menuRef?: React.RefObject<HTMLDivElement>;
  selectedText: string;
  onActionSelect: (actionType: 'copy' | 'share' | 'comment') => void;
  onCopy?: () => void; // Optional override for copy behavior
  // Annotation handlers - form açmak için
  onCreateNote?: () => void; // ✅ Not Al handler
  onCreateAnnotation?: () => void; // Şerh Ekle handler (mevcut)
  onCreateHighlight?: (color: string, style: 'background' | 'text') => void; // Renk ve stil parametresi eklendi
  onCreateBookmark?: () => void;
  onFindAnnotations?: () => void; // Şerh bul handler
  onClearAnnotations?: () => void; // Temizle handler
  // Seçim değiştiğinde color picker'ı kapatmak için key
  selectionKey?: string;
}

// ActionButton bileşeni
const ActionButton: React.FC<React.PropsWithChildren<{
  label: string;
  onClick?: () => void;
  hoverBgColor: string;
}>> = ({ children, label, onClick, hoverBgColor }) => {
  return (
    <button
      title={label}
      className="p-2 rounded text-[var(--text-color)]/80 hover:text-[var(--text-color)] transition-colors duration-150 flex items-center justify-center"
      onClick={onClick}
      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = hoverBgColor}
      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
    >
      {children}
    </button>
  );
};

export const TextActionMenu: React.FC<TextActionMenuProps> = ({
  isVisible,
  position,
  menuRef,
  selectedText,
  onActionSelect,
  onCopy,
  onCreateNote,
  onCreateAnnotation,
  onCreateHighlight,
  onCreateBookmark,
  onFindAnnotations,
  onClearAnnotations,
  selectionKey
}) => {
  // State for color picker
  const [showColorPicker, setShowColorPicker] = useState(false);

  // Ayrı text ve background color state'leri
  const [textColor, setTextColor] = useState(() => {
    return localStorage.getItem('ikra-highlight-text-color') || '#000000';
  });
  const [bgColor, setBgColor] = useState(() => {
    return localStorage.getItem('ikra-highlight-bg-color') || '#ffff00';
  });

  // Styles hook
  const { bgColors, getMenuStyles } = useReaderInteractionStyles();

  // Yeni seçim yapıldığında color picker'ı kapat
  useEffect(() => {
    setShowColorPicker(false);
  }, [selectionKey]);

  if (!isVisible || !position) {
    return null;
  }

  // Handle default copy function
  const handleCopy = () => {
    if (onCopy) {
      onCopy();
    } else {
      navigator.clipboard.writeText(selectedText || window.getSelection()?.toString() || '');
      onActionSelect('copy');
    }
  };

  // Handle highlight button click
  const handleHighlightClick = () => {
    setShowColorPicker(!showColorPicker);
  };

  // Handle color selection
  const handleTextColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const color = e.target.value;
    setTextColor(color);
    localStorage.setItem('ikra-highlight-text-color', color);
  };

  const handleBgColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const color = e.target.value;
    setBgColor(color);
    localStorage.setItem('ikra-highlight-bg-color', color);
  };

  const handleColorSelect = (color: string, style: 'background' | 'text') => {
    if (color === 'DELETE') {
      // Temizle işlemi
      setShowColorPicker(false);
      if (onClearAnnotations) {
        onClearAnnotations();
      }
    } else {
      // Normal renk seçimi - seçilen style'a göre rengi kullan
      const finalColor = style === 'text' ? textColor : bgColor;
      setShowColorPicker(false);

      if (onCreateHighlight) {
        onCreateHighlight(finalColor, style);
      }
    }
  };



  // Get menu styles using hook
  const menuStyle = getMenuStyles(position, isVisible);

  return (
    <>
      <div
        ref={menuRef}
        className="flex items-center justify-between p-1 rounded-lg shadow-lg border relative"
        style={menuStyle}
      >
        {/* Sol taraf - Annotation butonları */}
        <div className="flex items-center">
          {/* ✅ Not Al */}
          {onCreateNote && (
            <ActionButton label="Not Al" onClick={onCreateNote} hoverBgColor={bgColors.hover}>
              <FileText size={16} />
            </ActionButton>
          )}

          {/* Şerh Ekle */}
          {onCreateAnnotation && (
            <ActionButton label="Şerh Ekle" onClick={onCreateAnnotation} hoverBgColor={bgColors.hover}>
              <MessageSquare size={16} />
            </ActionButton>
          )}

          {/* Vurgula */}
          {onCreateHighlight && (
            <ActionButton label="Vurgula" onClick={handleHighlightClick} hoverBgColor={bgColors.hover}>
              <Highlighter size={16} />
            </ActionButton>
          )}

          {/* Yer İmi */}
          {onCreateBookmark && (
            <ActionButton label="Yer İmi" onClick={onCreateBookmark} hoverBgColor={bgColors.hover}>
              <Bookmark size={16} />
            </ActionButton>
          )}
        </div>

        {/* Sağ taraf - Şerh Bul, Kopyala ve Paylaş */}
        <div className="flex items-center">
          {/* Şerh Bul */}
          {onFindAnnotations && (
            <ActionButton label="Şerh Bul" onClick={onFindAnnotations} hoverBgColor={bgColors.hover}>
              <Search size={16} />
            </ActionButton>
          )}

          {/* Kopyala */}
          <ActionButton label="Kopyala" onClick={handleCopy} hoverBgColor={bgColors.hover}>
            <Copy size={16} />
          </ActionButton>

          {/* Paylaş */}
          <ActionButton label="Paylaş" onClick={() => onActionSelect('share')} hoverBgColor={bgColors.hover}>
            <Share2 size={16} />
          </ActionButton>
        </div>

        {/* Renk Seçici - TextActionMenu içinde */}
        {showColorPicker && onCreateHighlight && position && (
          <div
            className="absolute z-50"
            style={{
              // Ekran konumuna göre dinamik açılım
              ...(window.innerHeight - position.top > 300 ? {
                // Aşağıda yeterli yer var - ColorPicker aşağı açılsın
                top: '100%',
                marginTop: '1px',
              } : {
                // Aşağıda yer yok - ColorPicker yukarı açılsın
                bottom: '100%',
                marginBottom: '1px',
              }),
              left: '50%',
              transform: 'translateX(-50%)',
            }}
          >
            {/* Text ve Background Color Seçicileri */}
            <div className="p-4 space-y-4">
              {/* Text Color */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Palette size={16} />
                  <span className="text-sm">Text Color</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xs opacity-70">{hexToRgb(textColor)}</span>
                  <input
                    type="color"
                    value={textColor}
                    onChange={handleTextColorChange}
                    className="w-6 h-6 rounded border cursor-pointer"
                    title="Text rengi seçici"
                  />
                </div>
              </div>

              {/* Background Color */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Palette size={16} />
                  <span className="text-sm">Background Color</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xs opacity-70">{hexToRgb(bgColor)}</span>
                  <input
                    type="color"
                    value={bgColor}
                    onChange={handleBgColorChange}
                    className="w-6 h-6 rounded border cursor-pointer"
                    title="Background rengi seçici"
                  />
                </div>
              </div>

              {/* Önizleme */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Önizleme</span>
                <div
                  className="px-2 py-1 rounded text-xs border"
                  style={{
                    color: textColor,
                    backgroundColor: bgColor + '80' // %50 opacity
                  }}
                >
                  Örnek Metin
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2 border-t">
                <button
                  onClick={() => handleColorSelect(textColor, 'text')}
                  className="flex-1 px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                >
                  Text Highlight
                </button>
                <button
                  onClick={() => handleColorSelect(bgColor, 'background')}
                  className="flex-1 px-3 py-2 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors"
                >
                  Background Highlight
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};