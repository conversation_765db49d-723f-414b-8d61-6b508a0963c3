import { useState, useEffect } from 'react';
import { useTheme } from '@shared/context/ThemeContext';

interface CustomColorState {
  color: string;
  theme: string;
}

/**
 * Tema-aware özel renk yönetimi hook'u
 * Her tema için ayrı renk saklar
 */
export function useCustomColor() {
  const { currentTheme } = useTheme();
  const [customColors, setCustomColors] = useState<Record<string, string>>({});

  // LocalStorage key'i
  const STORAGE_KEY = 'ikra-custom-colors';

  // LocalStorage'dan renkleri yükle
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setCustomColors(parsed);
      }
    } catch (error) {
      console.warn('Custom colors could not be loaded from localStorage:', error);
    }
  }, []);

  // Mevcut tema için renk al
  const getCurrentColor = (): string => {
    return customColors[currentTheme.id] || getDefaultColorForTheme(currentTheme.id);
  };

  // Tema için varsayılan renk
  const getDefaultColorForTheme = (themeId: string): string => {
    switch (themeId) {
      case 'light':
        return '#3b82f6'; // Mavi
      case 'dark':
        return '#60a5fa'; // Açık mavi
      case 'krem':
        return '#f59e0b'; // Amber
      default:
        return '#3b82f6';
    }
  };

  // Mevcut tema için renk ayarla
  const setCurrentColor = (color: string) => {
    const newColors = {
      ...customColors,
      [currentTheme.id]: color
    };
    
    setCustomColors(newColors);
    
    // LocalStorage'a kaydet
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newColors));
    } catch (error) {
      console.warn('Custom colors could not be saved to localStorage:', error);
    }
  };

  // Tema-aware renk paleti
  const getThemeAwareColors = () => {
    const baseColors = [
      '#ef4444', // Red
      '#f97316', // Orange  
      '#f59e0b', // Amber
      '#eab308', // Yellow
      '#84cc16', // Lime
      '#22c55e', // Green
      '#10b981', // Emerald
      '#14b8a6', // Teal
      '#06b6d4', // Cyan
      '#0ea5e9', // Sky
      '#3b82f6', // Blue
      '#6366f1', // Indigo
      '#8b5cf6', // Violet
      '#a855f7', // Purple
      '#d946ef', // Fuchsia
      '#ec4899', // Pink
      '#f43f5e', // Rose
    ];

    // Tema göre renkleri ayarla
    if (currentTheme.id === 'dark') {
      // Karanlık temada daha açık tonlar
      return baseColors.map(color => {
        const hsl = hexToHsl(color);
        return hslToHex(hsl.h, Math.max(hsl.s - 10, 0), Math.min(hsl.l + 20, 90));
      });
    } else if (currentTheme.id === 'krem') {
      // Krem temada daha sıcak tonlar
      return baseColors.map(color => {
        const hsl = hexToHsl(color);
        return hslToHex(Math.min(hsl.h + 5, 360), Math.min(hsl.s + 5, 100), hsl.l);
      });
    }

    return baseColors;
  };

  return {
    currentColor: getCurrentColor(),
    setCurrentColor,
    themeAwareColors: getThemeAwareColors(),
    defaultColor: getDefaultColorForTheme(currentTheme.id),
    currentTheme: currentTheme.id
  };
}

// Yardımcı fonksiyonlar
function hexToHsl(hex: string): { h: number; s: number; l: number } {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
}

function hslToHex(h: number, s: number, l: number): string {
  h /= 360;
  s /= 100;
  l /= 100;

  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };

  let r, g, b;

  if (s === 0) {
    r = g = b = l;
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  const toHex = (c: number) => {
    const hex = Math.round(c * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}
