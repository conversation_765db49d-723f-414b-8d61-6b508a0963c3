import React from 'react';
import { useRgbColor } from '../context/RgbColorContext';
import RgbColorPicker from './RgbColorPicker';
import { useAdaptiveOverlay } from '../hooks/useColorSystem';

/**
 * Global RGB Picker Component
 * Uygulama genelinde kullanılabilen RGB renk seçici
 */
const GlobalRgbPicker: React.FC = () => {
  const { isRgbPickerOpen, closeRgbPicker, onColorSelect } = useRgbColor();
  const navBgColor = useAdaptiveOverlay(21);

  if (!isRgbPickerOpen) {
    return null;
  }

  return (
    <>
      {/* Arka Plan Overlay */}
      <div 
        className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-50 transition-opacity"
        onClick={closeRgbPicker}
      />
      
      {/* RGB Picker Paneli */}
      <div 
        className="fixed z-50 bottom-0 left-0 right-0 max-h-[80vh] md:top-[15%] md:left-1/2 md:transform md:-translate-x-1/2 md:w-[360px] md:max-h-[70vh] md:rounded-2xl rounded-t-2xl shadow-2xl border border-opacity-20 overflow-hidden flex flex-col"
        style={{
          backgroundColor: navBgColor,
          borderColor: 'color-mix(in srgb, var(--text-color) 15%, transparent)'
        }}
      >
        {/* Pull indicator for mobile */}
        <div className="md:hidden w-10 h-1 mx-auto mt-3 mb-1 rounded-full bg-[var(--text-color)] opacity-15 flex-shrink-0" />
        
        {/* Panel Header */}
        <div className="flex items-center justify-between p-3 border-b flex-shrink-0"
          style={{ borderColor: 'color-mix(in srgb, var(--text-color) 10%, transparent)' }}
        >
          <h3 className="text-base font-medium" style={{ color: 'var(--text-color)' }}>
            RGB Renk Seçici
          </h3>
          <button
            onClick={closeRgbPicker}
            className="p-1 rounded-lg hover:bg-[var(--text-color)]/10 transition-colors"
            style={{ color: 'var(--text-color)' }}
          >
            ✕
          </button>
        </div>

        {/* RGB Picker Content */}
        <div className="p-4 overflow-y-auto">
          {/* Bilgilendirme Mesajı */}
          <div className="mb-4 p-3 rounded-lg border" style={{
            backgroundColor: 'var(--text-color)/5',
            borderColor: 'var(--text-color)/10'
          }}>
            <p className="text-sm" style={{ color: 'var(--text-color)' }}>
              💡 <strong>Nasıl kullanılır:</strong><br />
              1. Önce bir metin seçin<br />
              2. Metin seçim menüsünden "Vurgula" butonuna tıklayın<br />
              3. Bu renk seçiciyi kullanarak istediğiniz rengi seçin
            </p>
          </div>

          <RgbColorPicker
            onColorSelect={onColorSelect || (() => {})}
            compact={true}
          />
        </div>
      </div>
    </>
  );
};

export default GlobalRgbPicker;
