import React, { useState, useCallback, memo } from 'react';
import { Check, Palette, RotateCcw } from 'lucide-react';
import { useAdaptiveOverlay } from '@shared/color-system/hooks/useColorSystem';

interface RgbColorPickerProps {
  /** Seçili renk */
  selectedColor?: string;
  /** Seçili stil tipi */
  selectedStyle?: 'background' | 'text';
  /** Renk seçildiğinde tetiklenecek fonksiyon */
  onColorSelect: (color: string, style: 'background' | 'text') => void;
  /** Kapatma fonksiyonu */
  onClose?: () => void;
  /** Özel CSS sınıfı */
  className?: string;
  /** Compact görünüm */
  compact?: boolean;
}

const RgbColorPicker: React.FC<RgbColorPickerProps> = ({
  selectedColor,
  selectedStyle = 'background',
  onColorSelect,
  onClose,
  className = '',
  compact = false
}) => {
  // RGB değerleri
  const [red, setRed] = useState(255);
  const [green, setGreen] = useState(0);
  const [blue, setBlue] = useState(0);
  const [alpha, setAlpha] = useState(1);

  // Tema-aware renkler
  const menuBgColor = useAdaptiveOverlay(21);
  const cardBgColor = useAdaptiveOverlay(12);
  const borderColor = useAdaptiveOverlay(8);
  const lightBorderColor = useAdaptiveOverlay(5);

  // Mevcut RGB rengi
  const currentColor = `rgb(${red}, ${green}, ${blue})`;
  const currentColorWithAlpha = `rgba(${red}, ${green}, ${blue}, ${alpha})`;

  // Renk değişim handler'ları
  const handleRedChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setRed(parseInt(e.target.value));
  }, []);

  const handleGreenChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setGreen(parseInt(e.target.value));
  }, []);

  const handleBlueChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setBlue(parseInt(e.target.value));
  }, []);

  const handleAlphaChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setAlpha(parseFloat(e.target.value));
  }, []);

  // Renk seçimi
  const handleColorSelect = useCallback((style: 'background' | 'text') => {
    const colorToUse = style === 'background' ? currentColorWithAlpha : currentColor;
    onColorSelect(colorToUse, style);
  }, [currentColor, currentColorWithAlpha, onColorSelect]);

  // Reset fonksiyonu
  const handleReset = useCallback(() => {
    setRed(255);
    setGreen(0);
    setBlue(0);
    setAlpha(1);
  }, []);

  // Hex renk input
  const [hexInput, setHexInput] = useState('#ff0000');

  const handleHexChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const hex = e.target.value;
    setHexInput(hex);
    
    if (hex.match(/^#[0-9A-F]{6}$/i)) {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      setRed(r);
      setGreen(g);
      setBlue(b);
    }
  }, []);

  // RGB'den Hex'e dönüştürme
  const rgbToHex = useCallback((r: number, g: number, b: number) => {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  }, []);

  // RGB değerleri değiştiğinde hex'i güncelle
  React.useEffect(() => {
    setHexInput(rgbToHex(red, green, blue));
  }, [red, green, blue, rgbToHex]);

  return (
    <>
      <style dangerouslySetInnerHTML={{
        __html: `
          .rgb-color-picker input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            background: transparent;
            cursor: pointer;
          }

          .rgb-color-picker input[type="range"]::-webkit-slider-track {
            height: 8px;
            border-radius: 4px;
          }

          .rgb-color-picker input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            height: 16px;
            width: 16px;
            border-radius: 50%;
            background: #ffffff;
            border: 2px solid #333;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          }

          .rgb-color-picker input[type="range"]::-moz-range-track {
            height: 8px;
            border-radius: 4px;
            border: none;
          }

          .rgb-color-picker input[type="range"]::-moz-range-thumb {
            height: 16px;
            width: 16px;
            border-radius: 50%;
            background: #ffffff;
            border: 2px solid #333;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
          }
        `
      }} />

      <div
        className={`rgb-color-picker p-4 border shadow-xl backdrop-blur-sm ${className}`}
        style={{
          backgroundColor: menuBgColor,
          borderColor: borderColor,
          width: compact ? '280px' : '320px',
          borderRadius: '16px'
        }}
      >
      {/* Header */}
      <div className="flex items-center gap-2 mb-4 pb-2 border-b" style={{ borderColor: lightBorderColor }}>
        <Palette size={16} style={{ color: 'var(--text-color)' }} />
        <span className="text-sm font-medium" style={{ color: 'var(--text-color)' }}>
          RGB Renk Seçici
        </span>
        <button
          onClick={handleReset}
          className="ml-auto p-1 rounded-lg hover:bg-[var(--text-color)]/10 transition-colors"
          title="Sıfırla"
        >
          <RotateCcw size={14} style={{ color: 'var(--text-color)' }} />
        </button>
      </div>

      {/* Renk Önizleme */}
      <div className="mb-4">
        <div
          className="w-full h-16 rounded-xl border-2 mb-2"
          style={{
            backgroundColor: currentColor,
            borderColor: borderColor
          }}
        />
        <div className="text-xs text-center opacity-70" style={{ color: 'var(--text-color)' }}>
          {currentColor}
        </div>
      </div>

      {/* RGB Sliders */}
      <div className="space-y-3 mb-4">
        {/* Red */}
        <div>
          <div className="flex justify-between items-center mb-1">
            <label className="text-xs font-medium" style={{ color: 'var(--text-color)' }}>
              Kırmızı
            </label>
            <span className="text-xs opacity-70" style={{ color: 'var(--text-color)' }}>
              {red}
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="255"
            value={red}
            onChange={handleRedChange}
            className="w-full h-2 rounded-lg appearance-none cursor-pointer slider-red"
            style={{
              background: `linear-gradient(to right, rgb(0, ${green}, ${blue}), rgb(255, ${green}, ${blue}))`,
            }}
          />
        </div>

        {/* Green */}
        <div>
          <div className="flex justify-between items-center mb-1">
            <label className="text-xs font-medium" style={{ color: 'var(--text-color)' }}>
              Yeşil
            </label>
            <span className="text-xs opacity-70" style={{ color: 'var(--text-color)' }}>
              {green}
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="255"
            value={green}
            onChange={handleGreenChange}
            className="w-full h-2 rounded-lg appearance-none cursor-pointer"
            style={{
              background: `linear-gradient(to right, rgb(${red}, 0, ${blue}), rgb(${red}, 255, ${blue}))`,
            }}
          />
        </div>

        {/* Blue */}
        <div>
          <div className="flex justify-between items-center mb-1">
            <label className="text-xs font-medium" style={{ color: 'var(--text-color)' }}>
              Mavi
            </label>
            <span className="text-xs opacity-70" style={{ color: 'var(--text-color)' }}>
              {blue}
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="255"
            value={blue}
            onChange={handleBlueChange}
            className="w-full h-2 rounded-lg appearance-none cursor-pointer"
            style={{
              background: `linear-gradient(to right, rgb(${red}, ${green}, 0), rgb(${red}, ${green}, 255))`,
            }}
          />
        </div>

        {/* Alpha */}
        <div>
          <div className="flex justify-between items-center mb-1">
            <label className="text-xs font-medium" style={{ color: 'var(--text-color)' }}>
              Şeffaflık
            </label>
            <span className="text-xs opacity-70" style={{ color: 'var(--text-color)' }}>
              {Math.round(alpha * 100)}%
            </span>
          </div>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={alpha}
            onChange={handleAlphaChange}
            className="w-full h-2 rounded-lg appearance-none cursor-pointer"
            style={{
              background: `linear-gradient(to right, transparent, ${currentColor})`,
            }}
          />
        </div>
      </div>

      {/* Hex Input */}
      <div className="mb-4">
        <label className="text-xs font-medium mb-1 block" style={{ color: 'var(--text-color)' }}>
          Hex Renk Kodu
        </label>
        <input
          type="text"
          value={hexInput}
          onChange={handleHexChange}
          className="w-full px-3 py-2 rounded-lg border text-sm"
          style={{
            backgroundColor: cardBgColor,
            borderColor: borderColor,
            color: 'var(--text-color)'
          }}
          placeholder="#ff0000"
        />
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <button
          onClick={() => handleColorSelect('background')}
          className="flex-1 flex items-center justify-center gap-2 py-2.5 rounded-xl border-2 transition-all duration-300 hover:scale-105"
          style={{
            backgroundColor: selectedStyle === 'background' ? currentColor + '20' : cardBgColor,
            borderColor: selectedStyle === 'background' ? currentColor : borderColor,
            color: 'var(--text-color)'
          }}
        >
          {selectedStyle === 'background' && <Check size={14} />}
          <span className="text-sm font-medium">Arka Plan</span>
        </button>

        <button
          onClick={() => handleColorSelect('text')}
          className="flex-1 flex items-center justify-center gap-2 py-2.5 rounded-xl border-2 transition-all duration-300 hover:scale-105"
          style={{
            backgroundColor: selectedStyle === 'text' ? currentColor + '20' : cardBgColor,
            borderColor: selectedStyle === 'text' ? currentColor : borderColor,
            color: selectedStyle === 'text' ? currentColor : 'var(--text-color)'
          }}
        >
          {selectedStyle === 'text' && <Check size={14} />}
          <span className="text-sm font-medium">Metin</span>
        </button>
      </div>
      </div>
    </>
  );
};

export default memo(RgbColorPicker);
