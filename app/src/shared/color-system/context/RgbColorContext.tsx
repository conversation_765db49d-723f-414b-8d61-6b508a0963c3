import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface RgbColorContextType {
  /** RGB picker açık mı? */
  isRgbPickerOpen: boolean;
  /** RGB picker'ı aç/kapat */
  toggleRgbPicker: () => void;
  /** RGB picker'ı kapat */
  closeRgbPicker: () => void;
  /** Seçili renk */
  selectedColor: string;
  /** Seçili stil */
  selectedStyle: 'background' | 'text';
  /** Renk seçim callback'i */
  onColorSelect?: (color: string, style: 'background' | 'text') => void;
  /** RGB picker'ı aç ve callback'i ayarla */
  openRgbPicker: (callback: (color: string, style: 'background' | 'text') => void) => void;
}

const RgbColorContext = createContext<RgbColorContextType | undefined>(undefined);

interface RgbColorProviderProps {
  children: ReactNode;
}

export const RgbColorProvider: React.FC<RgbColorProviderProps> = ({ children }) => {
  const [isRgbPickerOpen, setIsRgbPickerOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#ff0000');
  const [selectedStyle, setSelectedStyle] = useState<'background' | 'text'>('background');
  const [onColorSelect, setOnColorSelect] = useState<((color: string, style: 'background' | 'text') => void) | undefined>();

  const toggleRgbPicker = useCallback(() => {
    setIsRgbPickerOpen(prev => !prev);
  }, []);

  const closeRgbPicker = useCallback(() => {
    setIsRgbPickerOpen(false);
    setOnColorSelect(undefined);
  }, []);

  const openRgbPicker = useCallback((callback: (color: string, style: 'background' | 'text') => void) => {
    setOnColorSelect(() => callback);
    setIsRgbPickerOpen(true);
  }, []);

  const handleColorSelect = useCallback((color: string, style: 'background' | 'text') => {
    setSelectedColor(color);
    setSelectedStyle(style);
    
    if (onColorSelect) {
      onColorSelect(color, style);
    }
    
    closeRgbPicker();
  }, [onColorSelect, closeRgbPicker]);

  const value: RgbColorContextType = {
    isRgbPickerOpen,
    toggleRgbPicker,
    closeRgbPicker,
    selectedColor,
    selectedStyle,
    onColorSelect: handleColorSelect,
    openRgbPicker
  };

  return (
    <RgbColorContext.Provider value={value}>
      {children}
    </RgbColorContext.Provider>
  );
};

export const useRgbColor = (): RgbColorContextType => {
  const context = useContext(RgbColorContext);
  if (context === undefined) {
    throw new Error('useRgbColor must be used within a RgbColorProvider');
  }
  return context;
};
